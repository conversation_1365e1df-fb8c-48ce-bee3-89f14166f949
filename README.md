# Wer weiß denn sowas? - Hochzeitsedition

Eine interaktive Quiz-Anwendung für Hochzeiten, basierend auf dem beliebten TV-Format "Wer weiß denn sowas?".

## Features

- Kategoriebasiertes Quiz-System
- Unterstützung für zwei Spieler
- Masterfrage mit Einsatz-Mechanik
- Responsive Design für verschiedene Bildschirmgrößen
- Anpassbare Fragen über YAML-Dateien
- Anpassbare Spielerbilder

## Technologien

- Backend: Python mit Flask
- Frontend: HTML, CSS, JavaScript
- Styling: Tailwind CSS
- Datenformat: YAML für Fragen und Antworten

## Schnellstart

1. Klonen Sie das Repository
2. Installieren Sie die Abhängigkeiten: `pip install -r requirements.txt`
3. Starten Sie die Anwendung: `python app.py`
4. Öffnen Sie einen Browser und navigieren Sie zu `http://localhost:5000`

## Deployment

Für detaillierte Anweisungen zum Deployment, einschließlich Docker-Deployment auf einem Homeserver, siehe [DEPLOYMENT.md](DEPLOYMENT.md).

## Anpassung

### Fragen anpassen

Bearbeiten Sie die Datei `data/fragen.yaml`, um die Fragen und Kategorien anzupassen.

### Spielerbilder anpassen

Ersetzen Sie die Bilder im Verzeichnis `static/images/` mit Ihren eigenen Bildern.

### Spielkonfiguration anpassen

Bearbeiten Sie die Datei `config.py`, um verschiedene Spielparameter anzupassen:
- Publikumspunkte pro richtige Antwort (Standard: 10 Punkte)
- Spielerpunkte pro richtige Antwort (Standard: 500 Punkte)
- Moderator-Passwort
- Weitere Spieleinstellungen

Detaillierte Informationen finden Sie in [KONFIGURATION.md](KONFIGURATION.md).

## Lizenz

Dieses Projekt ist für den persönlichen Gebrauch bestimmt.
