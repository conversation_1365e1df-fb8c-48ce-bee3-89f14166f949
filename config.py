# Konfiguration für das Quiz-Spiel
# Diese Datei enthält alle konfigurierbaren Parameter für das Spiel

class GameConfig:
    """Konfiguration für das Quiz-Spiel"""
    
    # Publikumspunkte
    AUDIENCE_POINTS_PER_CORRECT_ANSWER = 10  # Punkte pro richtige Antwort für Publikumsteilnehmer
    
    # Spielerpunkte
    PLAYER_POINTS_PER_CORRECT_ANSWER = 500  # Punkte pro richtige Antwort für Spieler
    MASTER_QUESTION_POINTS = 1000  # Punkte für die Masterfrage
    TIEBREAKER_WINNER_POINTS = 1000  # Punkte für den Gewinner der Schätzfrage
    TIEBREAKER_TIE_POINTS = 500  # Punkte bei Gleichstand in der Schätzfrage
    
    # Moderator-Passwort
    MODERATOR_PASSWORD = 'quiz2023'
    
    # Spieler-Konfiguration
    DEFAULT_PLAYERS = [
        {'name': '<PERSON>', 'score': 0, 'color': 'blue', 'image': '/static/images/sue.jpg'},
        {'name': 'Fabi', 'score': 0, 'color': 'pink', 'image': '/static/images/fabi.jpg'}
    ]
    
    @classmethod
    def get_audience_points(cls):
        """Gibt die konfigurierten Publikumspunkte zurück"""
        return cls.AUDIENCE_POINTS_PER_CORRECT_ANSWER
    
    @classmethod
    def get_player_points(cls):
        """Gibt die konfigurierten Spielerpunkte zurück"""
        return cls.PLAYER_POINTS_PER_CORRECT_ANSWER
