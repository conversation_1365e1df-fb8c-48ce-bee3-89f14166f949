<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wer weiß denn sowas? - Hochzeitsedition</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Montserrat', 'Open Sans', 'sans-serif'],
                        'display': ['Montserrat', 'Open Sans', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#003b6f',         /* Dark blue from the image */
                        'primary-light': '#0056a4',   /* Medium blue */
                        'primary-dark': '#002a4e',    /* Darker blue */
                        'secondary': '#e63b19',       /* Orange-red from the image */
                        'secondary-light': '#ff5a3c', /* Lighter orange */
                        'secondary-dark': '#c32000', /* Darker orange */
                        'correct': '#00b300',        /* Green for correct answers */
                        'incorrect': '#e60000',      /* Red for incorrect answers */
                        'player1': '#0077FF',        /* Bright blue for player 1 */
                        'player2': '#FF3D00',        /* Bright orange-red for player 2 */
                        'neutral': '#6b7280',        /* Gray for neutral elements */
                        'background': '#001428',      /* Dark blue background from image */
                        'card-bg': '#003b6f',        /* Card background color */
                        'card-border': '#0077cc',    /* Card border color */
                    },
                    aspectRatio: {
                        '16/9': '16 / 9',
                    },
                    boxShadow: {
                        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
                    },
                    backdropBlur: {
                        'glass': '4px',
                    },
                }
            }
        }
    </script>

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Framer Motion for animations -->
    <script src="https://unpkg.com/framer-motion@10.12.16/dist/framer-motion.js"></script>

    <style>
        body {
            background-color: #001428;
            background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Cpath fill="%230056a4" fill-opacity="0.05" d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z"/%3E%3C/svg%3E');
            min-height: 100vh;
            color: white;
            font-family: 'Montserrat', 'Open Sans', sans-serif;
            font-weight: bold;
        }

        .glass-container {
            background: rgba(0, 59, 111, 0.9);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border-radius: 1rem;
            border: 2px solid #0077cc;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
        }

        .container {
            max-width: 1280px;
            margin: 0 auto;
            aspect-ratio: 16/9;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 1rem;
            height: 100%;
        }

        .category-card {
            background: linear-gradient(to bottom, #2C8ABD, #3D0D63);
            border-radius: 0.5rem;
            border: 7px solid #67CBEB;
            box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow: hidden;
            color: white;
            font-family: 'The Sans', sans-serif;
            font-weight: bold;
            aspect-ratio: 1/1; /* Make it square */
            word-wrap: break-word;
            text-align: center;
            height: 100%;
        }

        .category-winner {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0.5rem;
            background-color: rgb(213, 130, 33);
            z-index: 2;
        }

        .winner-icon {
            margin-bottom: 0.25rem;
        }

        .winner-icon img {
<!--            width: 2rem;-->
<!--            height: 2rem;-->
            border-radius: 50%;
            object-fit: cover;
        }

        .winner-points {
            font-weight: bold;
            color: white;
            font-size: 1.25rem;
        }

        .category-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 30px 0 rgba(31, 38, 135, 0.2);
        }

        .category-card.used {
            opacity: 0.1;
            cursor: not-allowed;
        }

        .category-card.used::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                rgba(0, 0, 0, 0.1),
                rgba(200, 0, 0, 0.1) 10px,
                rgba(200, 0, 0, 0.2) 10px,
                rgba(200, 0, 0, 0.2) 20px
            );
            z-index: 1;
        }

        .answer-button {
            background: linear-gradient(to bottom, #e63b19, #003b6f);
            border: 2px solid #0077cc;
            border-radius: 0.5rem;
            padding: 1.25rem 1.25rem 1.25rem 4rem; /* Extra padding on left for letter */
            margin: 0.75rem 0;
            width: 100%;
            text-align: left;
            font-size: 1.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            color: white;
        }

        .answer-letter {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.75rem;
            font-weight: bold;
            color: #FFD700; /* Gold base color */
            text-shadow:
                0 0 5px #FFA500, /* Orange glow */
                0 0 10px #FFA500,
                0 0 15px #FFA500,
                0 0 20px #FFA500;
            font-family: 'The Sans', sans-serif;
            z-index: 2; /* Ensure letter stays on top */
        }

        .answer-button.correct .answer-letter,
        .answer-button.incorrect .answer-letter {
            color: white;
            text-shadow:
                0 0 5px rgba(255, 255, 255, 0.8),
                0 0 10px rgba(255, 255, 255, 0.5);
        }

        .answer-button:hover {
            background-color: #0056a4;
            transform: translateX(5px);
        }

        .answer-button.active {
            background: linear-gradient(to bottom, #e6b800, #cc9900);
            border-color: #ffcc00;
            color: white;
            box-shadow: 0 0 10px rgba(255, 204, 0, 0.7);
        }

        .answer-button.correct {
            background: linear-gradient(to bottom, #00b300, #008000);
            color: white;
            border-color: #008000;
            animation: pulse-success 2s infinite;
        }

        .answer-button.incorrect {
            background: linear-gradient(to bottom, #e60000, #b30000);
            color: white;
            border-color: #b30000;
            animation: pulse-error 2s infinite;
        }

        @keyframes pulse-success {
            0% { box-shadow: 0 0 0 0 rgba(0, 179, 0, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 179, 0, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 179, 0, 0); }
        }

        @keyframes pulse-error {
            0% { box-shadow: 0 0 0 0 rgba(230, 0, 0, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(230, 0, 0, 0); }
            100% { box-shadow: 0 0 0 0 rgba(230, 0, 0, 0); }
        }

        .question-container {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .question-text {
            background: linear-gradient(135deg, #003b6f, #0077cc);
            color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            border: 2px solid #0077cc;
            margin-bottom: 2rem;
            font-size: 1.75rem;
            font-weight: 600;
            text-align: center;
            flex-grow: 0;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
        }

        .answers-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .action-button {
            background: linear-gradient(135deg, #FF3D00, #FF7E00);
            color: white;
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
            margin-top: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            align-self: center;
            box-shadow: 0 10px 15px -3px rgba(255, 61, 0, 0.3);
            border: none;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .action-button:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 15px 30px -5px rgba(255, 61, 0, 0.4);
            filter: brightness(1.1);
        }

        .action-button:active {
            transform: translateY(1px) scale(0.98);
        }

        .action-button.primary {
            background: linear-gradient(135deg, #0077FF, #00AAFF);
            box-shadow: 0 10px 15px -3px rgba(0, 119, 255, 0.3);
        }

        .action-button.primary:hover {
            box-shadow: 0 15px 30px -5px rgba(0, 119, 255, 0.4);
        }

        .action-button.success {
            background: linear-gradient(135deg, #10b981, #34d399);
            box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.3);
        }

        .action-button.success:hover {
            box-shadow: 0 15px 30px -5px rgba(16, 185, 129, 0.4);
        }

        .player-score {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 1.25rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            border: 2px solid;
        }

        .player-score.active {
            transform: scale(1.1);
            box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 10;
            border-width: 3px;
        }

        .player-score.active::before {
            content: '➤ Aktueller Spieler';
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #FFD700;
            color: #000;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
            white-space: nowrap;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .player1 {
            background: linear-gradient(135deg, #0077FF, #005BBF);
            color: white;
            border-color: #00AAFF;
        }

        .player2 {
            background: linear-gradient(135deg, #FF3D00, #CC3000);
            color: white;
            border-color: #FF7E00;
        }

        .player-image {
            width: 18rem;
<!--            height: 350px;-->
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid white;
            margin-right: 10px;
        }

        .player-image-large {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo-image {
            height: 180px;
<!--            width: auto;-->
<!--            max-width: 200px;-->
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
            object-fit: contain;
        }

        /* Result overlay for correct/incorrect answers */
        .result-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .result-overlay.fade-out {
            animation: fadeOut 0.5s ease forwards;
        }

        .result-content {
            padding: 3rem;
            border-radius: 1rem;
            text-align: center;
            animation: scaleIn 0.3s ease;
        }

        .correct-result {
            background: rgba(0, 179, 0, 0.9);
            border: 3px solid #008000;
        }

        .incorrect-result {
            background: rgba(230, 0, 0, 0.9);
            border: 3px solid #b30000;
        }

        .result-icon {
            font-size: 5rem;
            margin-bottom: 1rem;
        }

        .result-text {
            font-size: 3rem;
            font-weight: 700;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        @keyframes scaleIn {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* Audience results styles */
        .audience-results {
            border-radius: 0.5rem;
            background: rgba(0, 0, 0, 0.3);
            margin-top: 1.5rem;
            padding: 1rem;
            color: white;
            text-align: center;
            animation: fadeIn 0.5s ease-in-out;
        }

        .audience-points-indicator {
            font-size: 0.85rem;
            color: #FFD700;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 0.25rem;
            padding: 0.25rem 0.5rem;
            animation: fadeIn 0.5s ease-in-out;
        }

        .answer-stat {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(to right, rgba(230, 59, 25, 0.7), rgba(0, 59, 111, 0.7));
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            min-width: 80px;
            height: 40px;
        }

        .correct-stat {
            background: linear-gradient(to right, rgba(0, 179, 0, 0.7), rgba(0, 128, 0, 0.7));
            box-shadow: 0 0 10px rgba(0, 179, 0, 0.5);
        }

        .explanation-section {
            background: linear-gradient(135deg, rgba(0, 59, 111, 0.7), rgba(236, 72, 153, 0.7));
            border: 2px solid rgba(255, 255, 255, 0.1);
            animation: fadeIn 0.5s ease-in-out;
        }

        /* Question images */
        .question-image {
            text-align: center;
        }

        .question-image img {
            max-height: 300px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
            cursor: pointer;
            transition: transform 0.2s ease;
            border-radius: 8px;
        }

        .question-image img:hover {
            transform: scale(1.02);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.6);
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* Explanation images */
        .explanation-image img {
            max-height: 300px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .explanation-image img:hover {
            transform: scale(1.03);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.6);
        }

        /* Fullscreen image overlay */
        .fullscreen-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .fullscreen-overlay.active {
            opacity: 1;
            pointer-events: auto;
        }

        .fullscreen-image {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
            border: 3px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
        }

        .close-fullscreen {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .close-fullscreen:hover {
            background-color: rgba(255, 0, 0, 0.7);
        }

        .explanation-text {
            font-size: 1.1rem;
            line-height: 1.6;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .hidden {
            display: none;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateX(20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease forwards;
        }

        .animate-slide-up {
            animation: slideUp 0.5s ease forwards;
        }

        .animate-slide-in {
            animation: slideIn 0.5s ease forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* Additional animations for results page */
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0,-30px,0);
            }
            70% {
                transform: translate3d(0,-15px,0);
            }
            90% {
                transform: translate3d(0,-4px,0);
            }
        }

        .animate-bounce {
            animation: bounce 2s infinite;
        }

        @keyframes glow {
            0% {
                box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
            }
            50% {
                box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.6);
            }
            100% {
                box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
            }
        }

        .animate-glow {
            animation: glow 2s ease-in-out infinite;
        }

        /* Confetti animation */
        .confetti {
            position: fixed;
            width: 10px;
            height: 10px;
            background: #f0f;
            animation: confetti-fall 5s linear infinite;
            z-index: 1000;
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }

        /* Winner celebration effects */
        @keyframes celebration {
            0% {
                transform: scale(1) rotate(0deg);
            }
            25% {
                transform: scale(1.1) rotate(5deg);
            }
            50% {
                transform: scale(1.05) rotate(-5deg);
            }
            75% {
                transform: scale(1.1) rotate(3deg);
            }
            100% {
                transform: scale(1) rotate(0deg);
            }
        }

        .animate-celebration {
            animation: celebration 1s ease-in-out infinite;
        }
    </style>
</head>
<body class="bg-background">
    <!-- Fullscreen image overlay -->
    <div id="fullscreen-overlay" class="fullscreen-overlay">
        <div class="close-fullscreen" id="close-fullscreen">
            <i class="fas fa-times"></i>
        </div>
        <img id="fullscreen-image" class="fullscreen-image" src="" alt="Bild in Vollbild">
    </div>

    <div class="container mx-auto p-6">
        {% if game_state is defined %}
        <div class="flex justify-between items-center mb-8 animate-fade-in">
            <div class="player-score player1 {% if game_state.current_player == 0 %}active animate-pulse{% endif %}">
                <div class="flex flex-col items-center">
                    {% if game_state.players[0].image is defined and game_state.players[0].image %}
                    <img src="{{ game_state.players[0].image }}" alt="{{ game_state.players[0].name }}" class="player-image-large mb-2">
                    {% else %}
                    <i class="fas fa-user text-5xl mb-2"></i>
                    {% endif %}
                    <span>{{ game_state.players[0].name }}: {{ game_state.players[0].score }} Punkte</span>
                    {% if game_state.audience_points and game_state.audience_points[0] > 0 %}
                    <div class="audience-points-indicator">
                        <i class="fas fa-users text-xs mr-1"></i> <span>+{{ game_state.audience_points[0] }} Publikumspunkte</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="logo-container">
                <img src="/static/images/logo.jpg" alt="Wer weiß denn sowas?" class="logo-image">
            </div>

            <div class="player-score player2 {% if game_state.current_player == 1 %}active animate-pulse{% endif %}">
                <div class="flex flex-col items-center">
                    {% if game_state.players[1].image is defined and game_state.players[1].image %}
                    <img src="{{ game_state.players[1].image }}" alt="{{ game_state.players[1].name }}" class="player-image-large mb-2">
                    {% else %}
                    <i class="fas fa-user text-5xl mb-2"></i>
                    {% endif %}
                    <span>{{ game_state.players[1].name }}: {{ game_state.players[1].score }} Punkte</span>
                    {% if game_state.audience_points and game_state.audience_points[1] > 0 %}
                    <div class="audience-points-indicator">
                        <i class="fas fa-users text-xs mr-1"></i> <span>+{{ game_state.audience_points[1] }} Publikumspunkte</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <main class="glass-container p-6 animate-fade-in">
            {% block content %}{% endblock %}
        </main>

        {% if not hide_footer %}
        <footer class="text-center mt-8 text-white animate-fade-in">
            <p class="mb-4">Mit Liebe erstellt für die Hochzeit</p>
            <div class="flex justify-center space-x-4 mb-4">
                <a href="/qrcode" class="text-sm bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold py-2 px-4 rounded-full hover:from-cyan-600 hover:to-blue-600 transition-all duration-300">
                    <i class="fas fa-qrcode mr-1"></i> Publikumsteilnahme
                </a>
                {% if session.get('logged_in', False) %}
                <a href="/reset" class="text-sm text-yellow-300 hover:text-yellow-100 transition-colors duration-300" onclick="return confirmReset()">
                    <i class="fas fa-redo-alt mr-1"></i> Neues Spiel starten
                </a>
                <a href="/logout" class="text-sm text-yellow-300 hover:text-yellow-100 transition-colors duration-300">
                    <i class="fas fa-sign-out-alt mr-1"></i> Abmelden
                </a>
                {% else %}
                <a href="/login" class="text-sm text-yellow-300 hover:text-yellow-100 transition-colors duration-300">
                    <i class="fas fa-sign-in-alt mr-1"></i> Moderator Login
                </a>
                {% endif %}
            </div>
        </footer>
        {% endif %}
    </div>

    {% block scripts %}{% endblock %}

    <script>
        // Reset confirmation function
        function confirmReset() {
            return confirm('Möchten Sie wirklich ein neues Spiel starten? Alle aktuellen Spielstände gehen verloren!');
        }

        // Fullscreen image functionality
        document.addEventListener('DOMContentLoaded', function() {
            const fullscreenOverlay = document.getElementById('fullscreen-overlay');
            const fullscreenImage = document.getElementById('fullscreen-image');
            const closeFullscreen = document.getElementById('close-fullscreen');

            // Function to open fullscreen overlay
            window.openFullscreenImage = function(imageSrc) {
                fullscreenImage.src = imageSrc;
                fullscreenOverlay.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            };

            // Close fullscreen when clicking the close button
            closeFullscreen.addEventListener('click', function() {
                fullscreenOverlay.classList.remove('active');
                document.body.style.overflow = ''; // Restore scrolling
            });

            // Close fullscreen when clicking outside the image
            fullscreenOverlay.addEventListener('click', function(e) {
                if (e.target === fullscreenOverlay) {
                    fullscreenOverlay.classList.remove('active');
                    document.body.style.overflow = ''; // Restore scrolling
                }
            });

            // Close fullscreen when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && fullscreenOverlay.classList.contains('active')) {
                    fullscreenOverlay.classList.remove('active');
                    document.body.style.overflow = ''; // Restore scrolling
                }
            });
        });
    </script>
</body>
</html>
