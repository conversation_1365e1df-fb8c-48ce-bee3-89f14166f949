{% extends "layout.html" %}

{% block content %}
<div class="text-center mb-6">
    <h3 class="text-3xl font-bold mb-4 text-white font-display">Masterfrage - Übersicht</h3>
    <p class="text-lg text-gray-300 mb-6">
        Übersicht der Einsätze und Antworten vor der Auflösung
    </p>
</div>

<div class="question-container">
    <div class="question-text" id="question-text" style="background: linear-gradient(135deg, #003b6f, #0077cc);">
        {{ master.question.text }}
    </div>

    <div class="grid grid-cols-2 gap-8 mt-8">
        <!-- Player 1 -->
        <div class="glass-container p-4 rounded-xl">
            <div class="flex flex-col items-center">
                <img src="{{ game_state.players[0].image }}" alt="{{ game_state.players[0].name }}" class="w-16 h-16 rounded-full mb-2">
                <h4 class="text-xl font-bold text-player1">{{ game_state.players[0].name }}</h4>
                <p class="text-white mb-4">{{ game_state.players[0].score }} Punkte</p>

                <div class="w-full mb-4">
                    <h5 class="text-lg font-medium text-white mb-2">Einsatz:</h5>
                    <p class="text-2xl font-bold text-player1">{{ game_state.master_bets[0] }} Punkte</p>
                </div>

                <div class="w-full">
                    <h5 class="text-lg font-medium text-white mb-2">Antwort:</h5>
                    <div class="answer-button-small" style="background: linear-gradient(135deg, #e11d48, #be185d);">
                        <span class="answer-letter">{{ ['A', 'B', 'C'][game_state.master_answers[0]] }}</span>
                        {{ master.question.answers[game_state.master_answers[0]] }}
                    </div>
                </div>

                <!-- Audience bets for Player 1 -->
                <div class="w-full mt-4">
                    <h5 class="text-lg font-medium text-white mb-2">Publikum:</h5>
                    {% set player1_audience_count = 0 %}
                    {% set player1_audience_points = 0 %}
                    {% for audience_id, bet_data in game_state.audience_master_bets.items() %}
                        {% if bet_data.player == 0 %}
                            {% set player1_audience_count = player1_audience_count + 1 %}
                            {% set player1_audience_points = player1_audience_points + bet_data.bet %}
                        {% endif %}
                    {% endfor %}
                    <p class="text-white">{{ player1_audience_count }} Teilnehmer</p>
                    <p class="text-xl font-bold text-player1">{{ player1_audience_points }} Punkte gesetzt</p>
                </div>
            </div>
        </div>

        <!-- Player 2 -->
        <div class="glass-container p-4 rounded-xl">
            <div class="flex flex-col items-center">
                <img src="{{ game_state.players[1].image }}" alt="{{ game_state.players[1].name }}" class="w-16 h-16 rounded-full mb-2">
                <h4 class="text-xl font-bold text-player2">{{ game_state.players[1].name }}</h4>
                <p class="text-white mb-4">{{ game_state.players[1].score }} Punkte</p>

                <div class="w-full mb-4">
                    <h5 class="text-lg font-medium text-white mb-2">Einsatz:</h5>
                    <p class="text-2xl font-bold text-player2">{{ game_state.master_bets[1] }} Punkte</p>
                </div>

                <div class="w-full">
                    <h5 class="text-lg font-medium text-white mb-2">Antwort:</h5>
                    <div class="answer-button-small" style="background: linear-gradient(135deg, #e11d48, #be185d);">
                        <span class="answer-letter">{{ ['A', 'B', 'C'][game_state.master_answers[1]] }}</span>
                        {{ master.question.answers[game_state.master_answers[1]] }}
                    </div>
                </div>

                <!-- Audience bets for Player 2 -->
                <div class="w-full mt-4">
                    <h5 class="text-lg font-medium text-white mb-2">Publikum:</h5>
                    {% set player2_audience_count = 0 %}
                    {% set player2_audience_points = 0 %}
                    {% for audience_id, bet_data in game_state.audience_master_bets.items() %}
                        {% if bet_data.player == 1 %}
                            {% set player2_audience_count = player2_audience_count + 1 %}
                            {% set player2_audience_points = player2_audience_points + bet_data.bet %}
                        {% endif %}
                    {% endfor %}
                    <p class="text-white">{{ player2_audience_count }} Teilnehmer</p>
                    <p class="text-xl font-bold text-player2">{{ player2_audience_points }} Punkte gesetzt</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Audience Answer Distribution -->
    <div class="mt-8">
        <h4 class="text-xl font-bold text-white mb-4 text-center">Antwortverteilung Publikum</h4>
        <div class="flex justify-center space-x-4">
            {% for i in range(3) %}
            {% set answer_count = 0 %}
            {% for answer_index in game_state.audience_master_answers.values() %}
                {% if answer_index == i %}
                    {% set answer_count = answer_count + 1 %}
                {% endif %}
            {% endfor %}
            <div class="glass-container p-3 rounded-xl text-center w-24">
                <div class="answer-letter-small">{{ ['A', 'B', 'C'][i] }}</div>
                <p class="text-xl font-bold text-white mt-2">{{ answer_count }}</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="flex justify-center mt-8">
        <button class="action-button primary" id="reveal-button">Masterfrage auflösen</button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const revealButton = document.getElementById('reveal-button');

        revealButton.addEventListener('click', function() {
            // Call the API to reveal the master question
            fetch('/api/master-reveal', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect to the results page
                    window.location.href = '/results';
                } else {
                    alert('Es gab ein Problem bei der Auflösung: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error revealing master question:', error);
                alert('Es gab ein Problem bei der Auflösung. Bitte versuche es erneut.');
            });
        });
    });
</script>
{% endblock %}

{% block styles %}
<style>
    .answer-button-small {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        margin-bottom: 0.5rem;
        width: 100%;
        color: white;
        font-weight: 500;
    }

    .answer-letter-small {
        display: block;
        width: 1.5rem;
        height: 1.5rem;
        line-height: 1.5rem;
        text-align: center;
        border-radius: 50%;
        background-color: rgba(255, 200, 0, 0.9);
        color: black;
        font-weight: bold;
        margin: 0 auto;
    }
</style>
{% endblock %}
