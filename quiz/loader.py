import yaml
import random
import os
import json
from flask import current_app, session
from config import GameConfig

class GameState:
    """Manages the state of the quiz game"""
    def __init__(self):
        self.players = [
            {'name': '<PERSON>', 'score': 0, 'color': 'blue', 'image': '/static/images/sue.jpg'},
            {'name': 'Fabi', 'score': 0, 'color': 'pink', 'image': '/static/images/fabi.jpg'}
        ]
        self.current_player = 0  # Index of the current player (0 or 1)
        self.used_categories = []  # List of category slugs that have been used
        self.category_winners = {}  # Maps category slugs to player index and points won
        self.answered_categories = []  # List of category slugs that have been answered
        self.game_phase = 'categories'  # 'categories', 'question', 'master_bet', 'master_answer', 'tiebreaker', 'finished'
        self.master_bets = [0, 0]  # Bets for the master question
        self.master_answers = [None, None]  # Answers for the master question
        self.master_phase = 'bet'  # 'bet', 'answer', or 'overview'
        self.tiebreaker_guesses = [None, None]  # Guesses for the tiebreaker
        self.audience_points = [0, 0]  # Points from audience members for each player
        self.audience_members = {}  # Dictionary to track audience members and their votes
        self.audience_master_bets = {}  # Dictionary to track audience bets for master question
        self.audience_master_answers = {}  # Dictionary to track audience answers for master question

    def switch_player(self):
        """Switch to the other player"""
        self.current_player = 1 if self.current_player == 0 else 0

    def add_score(self, points, category_slug=None):
        """Add points to the current player's score"""
        self.players[self.current_player]['score'] += points

        # If a category is specified, record the winner
        if category_slug:
            self.category_winners[category_slug] = {
                'player': self.current_player,
                'points': points
            }

            # Mark the category as answered
            if category_slug not in self.answered_categories:
                self.answered_categories.append(category_slug)
                print(f"Category {category_slug} marked as answered")

    def mark_category_used(self, category_slug):
        """Mark a category as used"""
        if category_slug not in self.used_categories:
            self.used_categories.append(category_slug)

    def is_category_used(self, category_slug):
        """Check if a category has been used"""
        return category_slug in self.used_categories

    def all_categories_used(self, total_categories):
        """Check if all regular categories have been used"""
        # We need exactly 8 categories to be used before triggering the master question
        return len(self.used_categories) >= 8

    def set_master_bet(self, bet):
        """Set the bet for the master question"""
        self.master_bets[self.current_player] = bet

    def set_master_answer(self, answer_index):
        """Set the answer for the master question"""
        self.master_answers[self.current_player] = answer_index

    def process_master_result(self, correct_index):
        """Process the results of the master question"""
        for i in range(2):
            if self.master_answers[i] == correct_index:
                # Double the bet if correct
                self.players[i]['score'] += self.master_bets[i]
            else:
                # Lose the bet if incorrect
                self.players[i]['score'] -= self.master_bets[i]

        # Process audience master question answers
        self.process_audience_master_answers(correct_index)

    def store_audience_master_bet(self, audience_id, player_index, bet, answer_index):
        """Store an audience member's bet and answer for the master question

        Args:
            audience_id: Unique identifier for the audience member
            player_index: The player index (0 or 1) the audience member is betting on
            bet: The bet amount
            answer_index: The index of the selected answer

        Returns:
            dict: Result with success status
        """
        # Check if audience member is registered
        if audience_id not in self.audience_members:
            return {'success': False, 'message': 'Nicht registriert'}

        # Store the bet and answer
        self.audience_master_bets[audience_id] = {
            'player': player_index,
            'bet': bet
        }

        self.audience_master_answers[audience_id] = answer_index

        print(f"Audience member {audience_id} bet {bet} points on player {player_index + 1} with answer {answer_index}")

        return {
            'success': True,
            'message': 'Einsatz und Antwort gespeichert'
        }

    def process_audience_master_answers(self, correct_index):
        """Process all audience answers for the master question

        Args:
            correct_index: The index of the correct answer

        Returns:
            dict: Statistics about audience answers
        """
        total_answers = 0
        correct_answers = 0
        answer_counts = [0, 0, 0]  # Counts for options A, B, C
        points_per_player = [0, 0]  # Points earned for each player

        for audience_id, answer_index in self.audience_master_answers.items():
            total_answers += 1

            # Count the answer
            if 0 <= answer_index < len(answer_counts):
                answer_counts[answer_index] += 1

            # Check if correct
            is_correct = (answer_index == correct_index)
            if is_correct:
                correct_answers += 1

                # Check if this audience member placed a bet
                if audience_id in self.audience_master_bets:
                    bet_data = self.audience_master_bets[audience_id]
                    player_index = bet_data['player']
                    bet_amount = bet_data['bet']

                    # Award points to the player they bet on
                    self.audience_points[player_index] += bet_amount
                    points_per_player[player_index] += bet_amount

                    # Update audience member's score
                    self.audience_members[audience_id]['score'] += bet_amount

                    print(f"Audience member {audience_id} won {bet_amount} points for player {player_index + 1}")
            else:
                # If incorrect, they lose their bet
                if audience_id in self.audience_master_bets:
                    print(f"Audience member {audience_id} lost their bet")

        # Log results
        if total_answers > 0:
            correct_percentage = (correct_answers / total_answers) * 100
            print(f"Processed {total_answers} audience answers for master question")
            print(f"Correct answers: {correct_answers} ({correct_percentage:.1f}%)")
            print(f"Answer distribution: A: {answer_counts[0]}, B: {answer_counts[1]}, C: {answer_counts[2]}")
            print(f"Points added - Player 1: {points_per_player[0]}, Player 2: {points_per_player[1]}")
            print(f"Total audience points - Player 1: {self.audience_points[0]}, Player 2: {self.audience_points[1]}")

        return {
            'success': True,
            'total_answers': total_answers,
            'correct_answers': correct_answers,
            'answer_counts': answer_counts,
            'correct_index': correct_index,
            'points_per_player': points_per_player
        }

    def set_tiebreaker_guess(self, guess):
        """Set the guess for the tiebreaker question"""
        self.tiebreaker_guesses[self.current_player] = guess

    def process_tiebreaker_result(self, correct_answer):
        """Process the results of the tiebreaker"""
        # Calculate which player was closer to the correct answer
        diff0 = abs(self.tiebreaker_guesses[0] - correct_answer)
        diff1 = abs(self.tiebreaker_guesses[1] - correct_answer)

        if diff0 < diff1:
            # Player 0 wins
            self.players[0]['score'] += 1000
        elif diff1 < diff0:
            # Player 1 wins
            self.players[1]['score'] += 1000
        # If equal, both get points (unlikely but possible)
        else:
            self.players[0]['score'] += 500
            self.players[1]['score'] += 500

    def add_audience_member(self, audience_id):
        """Register an audience member

        Args:
            audience_id: Unique identifier for the audience member

        Returns:
            bool: True if the audience member was added, False if already registered
        """
        # Check if audience member has already registered
        if audience_id in self.audience_members:
            print(f"Audience member {audience_id} already registered")
            return True

        # Register the audience member
        self.audience_members[audience_id] = {
            'score': 0,
            'current_question': None,
            'current_answer': None,
            'pending_answers': {}
        }

        # Log the audience members to the console
        print(f"New audience member {audience_id} registered. Total: {len(self.audience_members)}")
        print(f"Audience points - Player 1: {self.audience_points[0]}, Player 2: {self.audience_points[1]}")

        return True

    def store_audience_answer(self, audience_id, category_slug, answer_index):
        """Store an answer from an audience member (without processing it yet)

        Args:
            audience_id: Unique identifier for the audience member
            category_slug: The category slug of the question
            answer_index: The index of the selected answer

        Returns:
            dict: Result with success status
        """
        # Check if audience member is registered
        if audience_id not in self.audience_members:
            return {'success': False, 'message': 'Nicht registriert'}

        audience_member = self.audience_members[audience_id]

        # Check if the category exists
        # We need to load the data here to avoid circular imports
        import os
        data_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'fragen.yaml')
        quiz_data = load_data(data_file)

        if category_slug not in quiz_data:
            return {'success': False, 'message': 'Kategorie nicht gefunden'}

        # Get the question
        category = quiz_data[category_slug]
        question = category.question if hasattr(category, 'question') else None

        if not question:
            return {'success': False, 'message': 'Keine Frage verfügbar'}

        # Store the pending answer
        if 'pending_answers' not in audience_member:
            audience_member['pending_answers'] = {}

        # Check if this category is already in category_winners (already revealed)
        if category_slug in self.category_winners:
            # Question has already been revealed, process it immediately
            is_correct = (answer_index == question.correct_index)
            if is_correct:
                # Award 25 points (reduced from 100) for correct answers
                audience_member['score'] += 25
                self.audience_points[self.current_player] += 25
                print(f"Late answer from audience member {audience_id} was correct! +25 points")
            else:
                print(f"Late answer from audience member {audience_id} was incorrect")

            # Update member's current question/answer
            audience_member['current_question'] = category_slug
            audience_member['current_answer'] = answer_index

            return {
                'success': True,
                'message': 'Antwort verarbeitet (Frage bereits aufgedeckt)',
                'correct': is_correct
            }
        else:
            # Store for later processing
            audience_member['pending_answers'][category_slug] = answer_index
            print(f"Audience member {audience_id} submitted answer {answer_index} for {category_slug}")

            return {
                'success': True,
                'message': 'Antwort gespeichert'
            }

    def process_audience_answers(self, category_slug):
        """Process all pending audience answers for a category when the moderator reveals the answer

        Args:
            category_slug: The category slug of the question

        Returns:
            dict: Statistics about audience answers
        """
        # Check if the category exists
        import os
        data_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'fragen.yaml')
        quiz_data = load_data(data_file)

        if category_slug not in quiz_data:
            return {'success': False, 'message': 'Kategorie nicht gefunden'}

        # Get the question
        category = quiz_data[category_slug]
        question = category.question if hasattr(category, 'question') else None

        if not question:
            return {'success': False, 'message': 'Keine Frage verfügbar'}

        correct_index = question.correct_index

        # Process all pending answers for this category
        total_answers = 0
        correct_answers = 0
        answer_counts = [0, 0, 0]  # Counts for options A, B, C

        for audience_id, member in self.audience_members.items():
            # Check if member has pending_answers dictionary
            if 'pending_answers' not in member:
                member['pending_answers'] = {}

            # Check if member has an answer for this category
            if category_slug in member['pending_answers']:
                answer_index = member['pending_answers'][category_slug]
                total_answers += 1

                # Count the answer
                if 0 <= answer_index < len(answer_counts):
                    answer_counts[answer_index] += 1

                # Check if correct
                is_correct = (answer_index == correct_index)
                if is_correct:
                    correct_answers += 1
                    # Award points (25 per correct answer - reduced from 100)
                    member['score'] += 25
                    # Add points to the current player
                    self.audience_points[self.current_player] += 25

                # Update member's current question/answer
                member['current_question'] = category_slug
                member['current_answer'] = answer_index

                # Remove from pending
                del member['pending_answers'][category_slug]

                # Debug output
                print(f"Processed answer for audience member {audience_id}: {is_correct}")

        # Log results
        if total_answers > 0:
            correct_percentage = (correct_answers / total_answers) * 100
            print(f"Processed {total_answers} audience answers for {category_slug}")
            print(f"Correct answers: {correct_answers} ({correct_percentage:.1f}%)")
            print(f"Answer distribution: A: {answer_counts[0]}, B: {answer_counts[1]}, C: {answer_counts[2]}")
            print(f"Added {correct_answers * 25} points to Player {self.current_player + 1}")
            print(f"Audience points - Player 1: {self.audience_points[0]}, Player 2: {self.audience_points[1]}")

        return {
            'success': True,
            'total_answers': total_answers,
            'correct_answers': correct_answers,
            'answer_counts': answer_counts,
            'correct_index': correct_index,
            'audience_points': self.audience_points[self.current_player]
        }

    def is_tie(self):
        """Check if the game is tied including audience points"""
        total_score_0 = self.players[0]['score'] + self.audience_points[0]
        total_score_1 = self.players[1]['score'] + self.audience_points[1]
        return total_score_0 == total_score_1

    def get_winner(self):
        """Get the winner of the game including audience points"""
        # Calculate total scores including audience points
        total_score_0 = self.players[0]['score'] + self.audience_points[0]
        total_score_1 = self.players[1]['score'] + self.audience_points[1]

        if total_score_0 > total_score_1:
            return 0
        elif total_score_1 > total_score_0:
            return 1
        else:
            return None  # Tie

    def to_dict(self):
        """Convert game state to dictionary for JSON serialization"""
        return {
            'players': self.players,
            'current_player': self.current_player,
            'used_categories': self.used_categories,
            'answered_categories': self.answered_categories,
            'category_winners': self.category_winners,
            'game_phase': self.game_phase,
            'master_phase': self.master_phase,
            'master_bets': self.master_bets,
            'master_answers': self.master_answers,
            'tiebreaker_guesses': self.tiebreaker_guesses,
            'audience_points': self.audience_points,
            'audience_members': self.audience_members,
            'audience_master_bets': self.audience_master_bets,
            'audience_master_answers': self.audience_master_answers
        }

    @classmethod
    def from_dict(cls, data):
        """Create a game state from a dictionary"""
        game_state = cls()
        game_state.players = data.get('players', game_state.players)
        game_state.current_player = data.get('current_player', 0)
        game_state.used_categories = data.get('used_categories', [])
        game_state.answered_categories = data.get('answered_categories', [])
        game_state.category_winners = data.get('category_winners', {})
        game_state.game_phase = data.get('game_phase', 'categories')
        game_state.master_phase = data.get('master_phase', 'bet')
        game_state.master_bets = data.get('master_bets', [0, 0])
        game_state.master_answers = data.get('master_answers', [None, None])
        game_state.tiebreaker_guesses = data.get('tiebreaker_guesses', [None, None])
        game_state.audience_points = data.get('audience_points', [0, 0])
        game_state.audience_members = data.get('audience_members', {})
        game_state.audience_master_bets = data.get('audience_master_bets', {})
        game_state.audience_master_answers = data.get('audience_master_answers', {})
        return game_state

class Question:
    """Represents a quiz question"""
    def __init__(self, text, answers, correct_index, explanation=None, question_image=None, answer_image=None):
        self.text = text
        self.answers = answers
        self.correct_index = correct_index
        self.explanation = explanation
        self.question_image = question_image  # Bild das bei der Frage angezeigt wird
        self.answer_image = answer_image      # Bild das bei der Antwort/Erklärung angezeigt wird

        # Rückwärtskompatibilität: Falls 'image' gesetzt ist, als answer_image verwenden
        if answer_image is None and hasattr(self, 'image'):
            self.answer_image = getattr(self, 'image', None)

    def to_dict(self):
        """Convert question to dictionary for JSON serialization"""
        return {
            'text': self.text,
            'answers': self.answers,
            'correct': self.correct_index,
            'explanation': self.explanation,
            'question_image': self.question_image,
            'answer_image': self.answer_image,
            # Rückwärtskompatibilität
            'image': self.answer_image
        }

class Category:
    """Represents a quiz category"""
    def __init__(self, name, icon, question=None):
        self.name = name
        self.icon = icon
        self.question = question
        self.used = False

    def to_dict(self):
        """Convert category to dictionary for JSON serialization"""
        return {
            'name': self.name,
            'icon': self.icon,
            'used': self.used
        }

def load_data(yaml_file):
    """Load quiz data from YAML file"""
    if not os.path.exists(yaml_file):
        return {}

    with open(yaml_file, 'r', encoding='utf-8') as file:
        data = yaml.safe_load(file)

    categories = {}

    for category_data in data:
        name = category_data.get('kategorie', '')
        icon = category_data.get('icon', 'question-mark')

        # Create a slug (URL-friendly version of the category name)
        slug = name.lower().replace(' ', '-').replace('ä', 'ae').replace('ö', 'oe').replace('ü', 'ue').replace('ß', 'ss')

        # Handle special categories (master question and tiebreaker)
        if slug in ['masterfrage', 'schaetzfrage']:
            if slug == 'masterfrage':
                # Bestimme Frage- und Antwortbild
                question_image = category_data.get('fragebild', None)
                answer_image = category_data.get('antwortbild', None)

                # Rückwärtskompatibilität: 'bild' als Antwortbild verwenden
                if answer_image is None:
                    answer_image = category_data.get('bild', None)

                question = Question(
                    category_data.get('text', ''),
                    category_data.get('antworten', []),
                    category_data.get('korrekt', 0),
                    category_data.get('erklaerung', None),
                    question_image,
                    answer_image
                )
            else:  # Schätzfrage
                question = {
                    'text': category_data.get('text', ''),
                    'answer': category_data.get('antwort', 0)
                }
            categories[slug] = {
                'name': name,
                'icon': icon,
                'question': question,
                'to_dict': lambda: {'name': name, 'icon': icon}  # Add a to_dict method for consistency
            }
        else:
            # Regular category with one question
            # Bestimme Frage- und Antwortbild
            question_image = category_data.get('fragebild', None)
            answer_image = category_data.get('antwortbild', None)

            # Rückwärtskompatibilität: 'bild' als Antwortbild verwenden
            if answer_image is None:
                answer_image = category_data.get('bild', None)

            question = Question(
                category_data.get('text', ''),
                category_data.get('antworten', []),
                category_data.get('korrekt', 0),
                category_data.get('erklaerung', None),
                question_image,
                answer_image
            )
            categories[slug] = Category(name, icon, question)

    return categories

# File path for storing game state
data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
# Create data directory if it doesn't exist
if not os.path.exists(data_dir):
    os.makedirs(data_dir)
game_state_file = os.path.join(data_dir, 'game_state.json')

def get_game_state():
    """Get the current game state from file or session"""
    # Try to load from file first
    if os.path.exists(game_state_file):
        try:
            with open(game_state_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return GameState.from_dict(data)
        except Exception as e:
            print(f"Error loading game state from file: {e}")

    # Fall back to session if file doesn't exist or can't be read
    if 'game_state' in session:
        return GameState.from_dict(session['game_state'])

    # Create new game state if neither exists
    new_state = GameState()
    save_game_state(new_state)
    return new_state

def save_game_state(game_state):
    """Save the game state to file and session"""
    # Save to session for backward compatibility
    session['game_state'] = game_state.to_dict()

    # Save to file for sharing between users
    try:
        with open(game_state_file, 'w', encoding='utf-8') as f:
            json.dump(game_state.to_dict(), f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"Error saving game state to file: {e}")

def reset_game():
    """Reset the game state"""
    # Remove from session
    if 'game_state' in session:
        del session['game_state']

    # Remove file if it exists
    if os.path.exists(game_state_file):
        try:
            os.remove(game_state_file)
        except Exception as e:
            print(f"Error removing game state file: {e}")
