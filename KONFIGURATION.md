# Konfiguration der Quiz-Anwendung

Diese Datei beschreibt, wie Sie die verschiedenen Parameter der Quiz-Anwendung anpassen können.

## Konfigurationsdatei

Die Hauptkonfiguration befindet sich in der Datei `config.py` im Hauptverzeichnis des Projekts.

## Verfügbare Einstellungen

### Publikumspunkte

- **`AUDIENCE_POINTS_PER_CORRECT_ANSWER`**: <PERSON><PERSON>, die Publikumsteilnehmer für jede richtige Antwort erhalten
  - Standard: 10 Punkte
  - Empfehlung: Je nach Anzahl der Mitspieler anpassen (weniger Punkte bei vielen Teilnehmern)

### Spielerpunkte

- **`PLAYER_POINTS_PER_CORRECT_ANSWER`**: Punkte für Spieler bei richtigen Antworten in normalen Kategorien
  - Standard: 500 Punkte

- **`MASTER_QUESTION_POINTS`**: Punkte für die Masterfrage (werden verdoppelt/verloren je nach Einsatz)
  - Standard: 1000 Punkte

- **`TIEBREAKER_WINNER_POINTS`**: Punkte für den Gewinner der Schätzfrage
  - Standard: 1000 Punkte

- **`TIEBREAKER_TIE_POINTS`**: Punkte bei Gleichstand in der Schätzfrage
  - Standard: 500 Punkte

### Sicherheit

- **`MODERATOR_PASSWORD`**: Passwort für den Moderator-Zugang
  - Standard: 'quiz2023'
  - **Wichtig**: Ändern Sie dieses Passwort vor dem Einsatz!

### Spieler-Konfiguration

- **`DEFAULT_PLAYERS`**: Standard-Spielerkonfiguration mit Namen, Farben und Bildern

## Beispiel-Anpassungen

### Publikumspunkte reduzieren

Wenn Sie viele Publikumsteilnehmer erwarten, können Sie die Punkte reduzieren:

```python
AUDIENCE_POINTS_PER_CORRECT_ANSWER = 5  # Statt 10
```

### Publikumspunkte erhöhen

Bei wenigen Teilnehmern können Sie die Punkte erhöhen:

```python
AUDIENCE_POINTS_PER_CORRECT_ANSWER = 20  # Statt 10
```

### Passwort ändern

```python
MODERATOR_PASSWORD = 'mein-sicheres-passwort-2024'
```

## Anwendung der Änderungen

1. Bearbeiten Sie die Datei `config.py`
2. Starten Sie die Anwendung neu
3. Die neuen Einstellungen werden sofort wirksam

**Hinweis**: Laufende Spiele verwenden weiterhin die alten Einstellungen. Starten Sie ein neues Spiel, um die neuen Konfigurationen zu verwenden.

## Empfehlungen nach Teilnehmerzahl

### Wenige Teilnehmer (1-10)
```python
AUDIENCE_POINTS_PER_CORRECT_ANSWER = 15
```

### Mittlere Teilnehmerzahl (11-25)
```python
AUDIENCE_POINTS_PER_CORRECT_ANSWER = 10  # Standard
```

### Viele Teilnehmer (26+)
```python
AUDIENCE_POINTS_PER_CORRECT_ANSWER = 5
```

Diese Empfehlungen sorgen dafür, dass die Publikumspunkte einen angemessenen Einfluss auf das Spielergebnis haben, ohne es zu dominieren.
